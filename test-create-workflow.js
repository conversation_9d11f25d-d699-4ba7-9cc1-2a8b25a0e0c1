const fetch = require('node-fetch');

async function testCreateWorkflow() {
  try {
    // Create a simple test workflow with a webhook trigger and HTTP request
    const workflow = {
      name: "Test Workflow - MCP Integration",
      nodes: [
        {
          id: "webhook-trigger",
          name: "Webhook Trigger",
          type: "n8n-nodes-base.webhook",
          typeVersion: 2,
          position: [200, 300],
          parameters: {
            path: "test-webhook",
            httpMethod: "GET",
            responseMode: "responseNode"
          }
        },
        {
          id: "http-request",
          name: "HTTP Request",
          type: "n8n-nodes-base.httpRequest",
          typeVersion: 4.2,
          position: [400, 300],
          parameters: {
            url: "https://jsonplaceholder.typicode.com/posts/1",
            method: "GET"
          }
        },
        {
          id: "respond-webhook",
          name: "Respond to Webhook",
          type: "n8n-nodes-base.respondToWebhook",
          typeVersion: 1,
          position: [600, 300],
          parameters: {
            respondWith: "json",
            responseBody: "={{ $json }}"
          }
        }
      ],
      connections: {
        "Webhook Trigger": {
          main: [
            [
              {
                node: "HTTP Request",
                type: "main",
                index: 0
              }
            ]
          ]
        },
        "HTTP Request": {
          main: [
            [
              {
                node: "Respond to Webhook",
                type: "main",
                index: 0
              }
            ]
          ]
        }
      }
    };

    const response = await fetch('http://localhost:3000/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer secure123'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'n8n_create_workflow',
          arguments: workflow
        }
      })
    });

    const result = await response.json();
    console.log('Create Workflow Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.result && result.result.content) {
      const content = JSON.parse(result.result.content[0].text);
      if (content.success) {
        console.log(`\n✅ Workflow created successfully!`);
        console.log(`Workflow ID: ${content.data.id}`);
        console.log(`Webhook URL: http://localhost:5678/webhook/test-webhook`);
      }
    }
  } catch (error) {
    console.error('Error creating workflow:', error);
  }
}

testCreateWorkflow();
