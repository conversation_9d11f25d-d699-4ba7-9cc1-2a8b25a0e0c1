const fetch = require('node-fetch');

async function testApiAuth() {
  const apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZDc4NTVhMy03ODYwLTRjOTUtYjdjZC0xNjM5NWNmNDI5ZGMiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNzExNzk0fQ.kdz2PdEz56_7bY53P6qtuMZA6T8hvdi9G_lHnVSvJyE';
  
  try {
    console.log('Testing different authentication methods...');
    
    // Method 1: Bearer token in Authorization header
    console.log('\n1. Testing Bearer token in Authorization header (/api):');
    const response1 = await fetch('http://localhost:5678/api/v1/workflows', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('Status:', response1.status);
    const data1 = await response1.text();
    console.log('Response:', data1);
    
    // Method 2: API key as query parameter
    console.log('\n2. Testing API key as query parameter (/api):');
    const response2 = await fetch(`http://localhost:5678/api/v1/workflows?apiKey=${apiKey}`);
    console.log('Status:', response2.status);
    const data2 = await response2.text();
    console.log('Response:', data2);
    
    // Method 3: X-N8N-API-KEY header
    console.log('\n3. Testing X-N8N-API-KEY header (/api):');
    const response3 = await fetch('http://localhost:5678/api/v1/workflows', {
      headers: {
        'X-N8N-API-KEY': apiKey,
        'Content-Type': 'application/json'
      }
    });
    console.log('Status:', response3.status);
    const data3 = await response3.text();
    console.log('Response:', data3);
    
    // Method 4: Check if we need to enable API access
    console.log('\n4. Testing health endpoint (/api):');
    const response4 = await fetch('http://localhost:5678/api/v1/health');
    console.log('Status:', response4.status);
    const data4 = await response4.text();
    console.log('Response:', data4);
    
  } catch (error) {
    console.error('Error testing API auth:', error);
  }
}

testApiAuth();
