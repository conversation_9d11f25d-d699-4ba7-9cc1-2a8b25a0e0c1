const fetch = require('node-fetch');

async function testDirectAPI() {
  try {
    console.log('Testing direct n8n API access...');
    
    // Test without API key first
    const response1 = await fetch('http://localhost:5678/api/workflows');
    console.log('Response status (no auth):', response1.status);
    console.log('Response headers:', Object.fromEntries(response1.headers.entries()));
    
    if (response1.status === 401) {
      console.log('API requires authentication');
    } else {
      const data1 = await response1.text();
      console.log('Response body (no auth):', data1);
    }
    
    // Test with API key
    console.log('\nTesting with API key...');
    const response2 = await fetch('http://localhost:5678/api/workflows', {
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkMDlhYjM1MS00YWQyLTQzMTAtYmQ4Yy00MjFmODAyNTY5NzciLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNjkzODY4LCJleHAiOjE3NTQyODAwMDB9.Y6QQFn_4gwRDPjGljRAjeiQ25RDoGflxBV5JI8q7jn0'
      }
    });
    console.log('Response status (with auth):', response2.status);
    const data2 = await response2.text();
    console.log('Response body (with auth):', data2);
    
  } catch (error) {
    console.error('Error testing direct API:', error);
  }
}

testDirectAPI();
