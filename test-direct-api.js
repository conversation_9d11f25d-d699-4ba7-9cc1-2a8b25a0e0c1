const fetch = require('node-fetch');

async function testDirectAPI() {
  try {
    console.log('Testing direct n8n API access...');
    
    // Test without API key first
    const response1 = await fetch('http://localhost:5678/rest/workflows');
    console.log('Response status (no auth):', response1.status);

    if (response1.status === 401) {
      console.log('API requires authentication');
      const data1 = await response1.text();
      console.log('Response body (no auth):', data1);
    } else {
      const data1 = await response1.json();
      console.log('Response body (no auth):', data1);
    }

    // Test with API key
    console.log('\nTesting with API key...');
    const response2 = await fetch('http://localhost:5678/rest/workflows', {
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZDc4NTVhMy03ODYwLTRjOTUtYjdjZC0xNjM5NWNmNDI5ZGMiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNzExNDkzLCJleHAiOjE3NTQyODAwMDB9.-l9NxqcfKQ773NgHTVRZoIJfhqToZ8WwMq_gmAgkal8'
      }
    });
    console.log('Response status (with auth):', response2.status);

    if (response2.status === 200) {
      const data2 = await response2.json();
      console.log('Response body (with auth):', JSON.stringify(data2, null, 2));
    } else {
      const data2 = await response2.text();
      console.log('Response body (with auth):', data2);
    }
    
  } catch (error) {
    console.error('Error testing direct API:', error);
  }
}

testDirectAPI();
