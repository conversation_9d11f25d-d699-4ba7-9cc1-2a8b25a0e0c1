// Test if the MCP server works in stdio mode
const { spawn } = require('child_process');

console.log('🧪 Testing n8n-mcp in stdio mode...\n');

// Set up environment
const env = {
  ...process.env,
  MCP_MODE: 'stdio',
  N8N_BASE_URL: 'http://localhost:5678',
  N8N_API_URL: 'http://localhost:5678/rest',
  N8N_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZDc4NTVhMy03ODYwLTRjOTUtYjdjZC0xNjM5NWNmNDI5ZGMiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNzExNzk0fQ.kdz2PdEz56_7bY53P6qtuMZA6T8hvdi9G_lHnVSvJyE'
};

// Start the MCP server
const mcpProcess = spawn('node', ['dist/mcp/index.js'], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseData = '';

mcpProcess.stdout.on('data', (data) => {
  responseData += data.toString();
  console.log('📤 Server response:', data.toString());
});

mcpProcess.stderr.on('data', (data) => {
  console.log('❌ Server error:', data.toString());
});

mcpProcess.on('close', (code) => {
  console.log(`\n🏁 MCP server exited with code ${code}`);
});

// Send initialization request
setTimeout(() => {
  console.log('📨 Sending initialization request...');
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(initRequest) + '\n');
}, 1000);

// Send tools list request
setTimeout(() => {
  console.log('📨 Sending tools list request...');
  const toolsRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };
  
  mcpProcess.stdin.write(JSON.stringify(toolsRequest) + '\n');
}, 2000);

// Clean up after 5 seconds
setTimeout(() => {
  console.log('\n🛑 Stopping test...');
  mcpProcess.kill();
}, 5000);
