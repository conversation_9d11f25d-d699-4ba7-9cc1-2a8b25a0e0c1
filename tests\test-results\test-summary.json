{"totalTests": 6, "passed": 6, "failed": 0, "startTime": "2025-06-08T10:57:55.233Z", "endTime": "2025-06-08T10:57:59.249Z", "tests": [{"name": "Basic Node Extraction", "status": "passed", "startTime": "2025-06-08T10:57:55.236Z", "endTime": "2025-06-08T10:57:55.342Z", "error": null, "details": {"results": [{"nodeType": "@n8n/n8n-nodes-langchain.Agent", "extracted": false, "error": "Node source code not found for: @n8n/n8n-nodes-langchain.Agent"}, {"nodeType": "n8n-nodes-base.Function", "extracted": true, "codeLength": 7449, "hasCredentials": false, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/Function/Function.node.js"}, {"nodeType": "n8n-nodes-base.Webhook", "extracted": true, "codeLength": 10667, "hasCredentials": false, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/Webhook/Webhook.node.js"}], "successCount": 2, "totalTested": 3}}, {"name": "List Available Nodes", "status": "passed", "startTime": "2025-06-08T10:57:55.342Z", "endTime": "2025-06-08T10:57:55.689Z", "error": null, "details": {"totalNodes": 439, "packages": ["unknown"], "nodesByPackage": {"unknown": ["ActionNetwork", "ActiveCampaign", "ActiveCampaignTrigger", "AcuitySchedulingTrigger", "<PERSON><PERSON>", "Affinity", "AffinityTrigger", "AgileCrm", "Airtable", "AirtableTrigger", "AirtableV1", "Amqp", "AmqpTrigger", "ApiTemplateIo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automizy", "Autopilot", "AutopilotTrigger", "AwsLambda", "AwsSns", "AwsSnsTrigger", "AwsCertificateManager", "AwsComprehend", "AwsDynamoDB", "AwsElb", "AwsRekognition", "AwsS3", "AwsS3V1", "AwsS3V2", "AwsSes", "AwsSqs", "AwsTextract", "AwsTranscribe", "<PERSON><PERSON><PERSON>", "Baserow", "<PERSON><PERSON><PERSON>", "BitbucketTrigger", "Bitly", "Bitwarden", "Box", "BoxTrigger", "Brandfetch", "Brevo", "BrevoTrigger", "Bubble", "CalTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chargebee", "ChargebeeTrigger", "CircleCi", "CiscoWebex", "CiscoWebexTrigger", "CitrixAdc", "Clearbit", "ClickUp", "ClickUpTrigger", "Clockify", "ClockifyTrigger", "Cloudflare", "Cockpit", "Coda", "Code", "CoinGecko", "CompareDatasets", "Compression", "Contentful", "ConvertKit", "ConvertKitTrigger", "Copper", "CopperTrigger", "<PERSON>rtex", "CrateDb", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CrowdDevTrigger", "Crypto", "CustomerIo", "Customer<PERSON>oTrigger", "DateTime", "DateTimeV1", "DateTimeV2", "DebugHelper", "DeepL", "De<PERSON>", "Dhl", "Discord", "Discourse", "<PERSON><PERSON><PERSON><PERSON>", "Drift", "Dropbox", "Dropcontact", "E2eTest", "ERPNext", "EditImage", "Egoi", "ElasticSecurity", "Elasticsearch", "EmailReadImap", "EmailReadImapV1", "EmailReadImapV2", "EmailSend", "EmailSendV1", "EmailSendV2", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventbriteTrigger", "ExecuteCommand", "ExecuteWorkflow", "ExecuteWorkflowTrigger", "ExecutionData", "FacebookGraphApi", "FacebookTrigger", "FacebookLeadAdsTrigger", "FigmaTrigger", "FileMaker", "Filter", "Flow", "FlowTrigger", "FormTrigger", "FormIoTrigger", "FormstackTrigger", "Freshdesk", "Freshservice", "FreshworksCrm", "Ftp", "Function", "FunctionItem", "GetResponse", "GetResponseTrigger", "Ghost", "Git", "<PERSON><PERSON><PERSON>", "GithubTrigger", "Gitlab", "GitlabTrigger", "GoToWebinar", "GoogleAds", "GoogleAnalytics", "GoogleAnalyticsV1", "GoogleBigQuery", "GoogleBigQueryV1", "GoogleBooks", "GoogleCalendar", "GoogleCalendarTrigger", "GoogleChat", "GoogleCloudNaturalLanguage", "GoogleCloudStorage", "GoogleContacts", "GoogleDocs", "GoogleDrive", "GoogleDriveTrigger", "GoogleDriveV1", "GoogleFirebaseCloudFirestore", "GoogleFirebaseRealtimeDatabase", "GSuiteAdmin", "Gmail", "GmailTrigger", "GmailV1", "GmailV2", "GooglePerspective", "GoogleSheets", "GoogleSheetsTrigger", "GoogleSlides", "GoogleTasks", "GoogleTranslate", "YouTube", "Gotify", "<PERSON><PERSON>", "GraphQL", "Grist", "GumroadTrigger", "HackerNews", "HaloPSA", "Harvest", "HelpScout", "HelpScoutTrigger", "HighLevel", "HomeAssistant", "Html", "HtmlExtract", "HttpRequest", "HttpRequestV1", "HttpRequestV2", "HttpRequestV3", "Hubspot", "HubspotTrigger", "HubspotV1", "HubspotV2", "HumanticAi", "<PERSON>", "ICalendar", "If", "Intercom", "Interval", "InvoiceNinja", "InvoiceNinjaTrigger", "ItemLists", "ItemListsV1", "ItemListsV2", "Iterable", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JotFormTrigger", "Kafka", "KafkaTrigger", "Keap", "KeapTrigger", "<PERSON><PERSON><PERSON>", "KoBoToolbox", "KoBoToolboxTrigger", "Ldap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Line", "Linear", "LinearTrigger", "LingvaNex", "LinkedIn", "LocalFileTrigger", "LoneScale", "LoneScaleTrigger", "Mqtt", "MqttTrigger", "Magento2", "Mailcheck", "Mailchimp", "MailchimpTrigger", "MailerLite", "MailerLiteTrigger", "Mailgun", "Mailjet", "MailjetTrigger", "Mandrill", "ManualTrigger", "<PERSON><PERSON>", "Marketstack", "Matrix", "Mattermost", "Mautic", "MauticTrigger", "Medium", "<PERSON><PERSON>", "MergeV1", "MergeV2", "MessageBird", "Metabase", "MicrosoftDynamicsCrm", "MicrosoftExcel", "MicrosoftExcelV1", "MicrosoftGraphSecurity", "MicrosoftOneDrive", "MicrosoftOutlook", "MicrosoftOutlookV1", "MicrosoftSql", "MicrosoftTeams", "MicrosoftToDo", "<PERSON><PERSON>", "Misp", "Mocean", "MondayCom", "MongoDb", "MonicaCrm", "MoveBinaryData", "Msg91", "MySql", "MySqlV1", "N8n", "N8nTrainingCustomerDatastore", "N8nTrainingCustomerMessenger", "N8nTrigger", "<PERSON><PERSON>", "Netlify", "NetlifyTrigger", "NextCloud", "NoOp", "NocoDB", "Notion", "NotionTrigger", "Npm", "Odoo", "OneSimpleApi", "Onfleet", "OnfleetTrigger", "OpenAi", "OpenThesaurus", "OpenWeatherMap", "Orbit", "Our<PERSON>", "Paddle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PayPal", "PayPalTrigger", "<PERSON><PERSON><PERSON><PERSON>", "Phantombuster", "PhilipsHue", "Pipedrive", "PipedriveTrigger", "Plivo", "PostBin", "PostHog", "Postgres", "PostgresTrigger", "PostgresV1", "PostmarkTrigger", "ProfitWell", "Pushbullet", "<PERSON><PERSON><PERSON>", "PushcutTrigger", "<PERSON><PERSON><PERSON>", "QuestDb", "QuickBase", "QuickBooks", "<PERSON><PERSON><PERSON>", "RabbitMQ", "RabbitMQTrigger", "Raindrop", "ReadBinaryFile", "ReadBinaryFiles", "ReadPDF", "Reddit", "Redis", "RedisTrigger", "Rename<PERSON><PERSON><PERSON>", "RespondToWebhook", "Rocketchat", "RssFeedRead", "RssFeedReadTrigger", "Rundeck", "S3", "Salesforce", "Salesmate", "ScheduleTrigger", "SeaTable", "SeaTableTrigger", "SecurityScorecard", "Segment", "SendGrid", "Sendy", "SentryIo", "ServiceNow", "Set", "SetV1", "SetV2", "Shopify", "ShopifyTrigger", "Signl4", "<PERSON><PERSON>ck", "SlackV1", "SlackV2", "Sms77", "Snowflake", "SplitInBatches", "SplitInBatchesV1", "SplitInBatchesV2", "SplitInBatchesV3", "<PERSON><PERSON><PERSON><PERSON>", "Spontit", "Spotify", "SpreadsheetFile", "SseTrigger", "Ssh", "Stackby", "Start", "StickyNote", "StopAndError", "Storyblok", "<PERSON><PERSON><PERSON>", "Strava", "StravaTrigger", "Stripe", "StripeTrigger", "Supabase", "SurveyMonkeyTrigger", "Switch", "SwitchV1", "SwitchV2", "SyncroMsp", "Taiga", "TaigaTrigger", "Tapfiliate", "Telegram", "TelegramTrigger", "TheHive", "TheHiveTrigger", "TheHiveProjectTrigger", "TimescaleDb", "Todoist", "TodoistV1", "TodoistV2", "TogglTrigger", "Totp", "TravisCi", "Trello", "TrelloTrigger", "Twake", "<PERSON><PERSON><PERSON>", "Twist", "Twitter", "TwitterV1", "TwitterV2", "TypeformTrigger", "UProc", "UnleashedSoftware", "Uplead", "UptimeRobot", "UrlScanIo", "VenafiTlsProtectDatacenter", "VenafiTlsProtectDatacenterTrigger", "VenafiTlsProtectCloud", "VenafiTlsProtectCloudTrigger", "Vero", "Vonage", "Wait", "Webflow", "WebflowTrigger", "Webhook", "<PERSON><PERSON>", "WhatsApp", "<PERSON>", "Wise<PERSON><PERSON>ger", "WooCommerce", "WooCommerceTrigger", "Wordpress", "WorkableTrigger", "WorkflowTrigger", "WriteBinaryFile", "Wu<PERSON>oTrigger", "Xero", "Xml", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zendesk", "ZendeskTrigger", "ZohoCrm", "Zoom", "Zulip"]}, "sampleNodes": [{"name": "ActionNetwork", "displayName": "Action Network", "description": "Consume the Action Network API", "location": "node_modules/n8n-nodes-base/dist/nodes/ActionNetwork/ActionNetwork.node.js"}, {"name": "ActiveCampaign", "displayName": "ActiveCampaign", "description": "Create and edit data in ActiveCampaign", "location": "node_modules/n8n-nodes-base/dist/nodes/ActiveCampaign/ActiveCampaign.node.js"}, {"name": "ActiveCampaignTrigger", "displayName": "ActiveCampaign Trigger", "description": "Handle ActiveCampaign events via webhooks", "location": "node_modules/n8n-nodes-base/dist/nodes/ActiveCampaign/ActiveCampaignTrigger.node.js"}, {"name": "AcuitySchedulingTrigger", "displayName": "Acuity <PERSON>ulin<PERSON>", "description": "Handle Acuity Scheduling events via webhooks", "location": "node_modules/n8n-nodes-base/dist/nodes/AcuityScheduling/AcuitySchedulingTrigger.node.js"}, {"name": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "description": "Consume Adalo API", "location": "node_modules/n8n-nodes-base/dist/nodes/Adalo/Adalo.node.js"}]}}, {"name": "Bulk Node Extraction", "status": "passed", "startTime": "2025-06-08T10:57:55.689Z", "endTime": "2025-06-08T10:57:58.574Z", "error": null, "details": {"totalAttempted": 10, "successCount": 6, "failureCount": 4, "timeElapsed": 2581, "results": [{"success": true, "data": {"nodeType": "ActionNetwork", "name": "ActionNetwork", "codeLength": 15810, "codeHash": "c0a880f5754b6b532ff787bdb253dc49ffd7f470f28aeddda5be0c73f9f9935f", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/ActionNetwork/ActionNetwork.node.js", "extractedAt": "2025-06-08T10:57:56.009Z"}}, {"success": true, "data": {"nodeType": "ActiveCampaign", "name": "ActiveCampaign", "codeLength": 38399, "codeHash": "5ea90671718d20eecb6cddae2e21c91470fdb778e8be97106ee2539303422ad2", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/ActiveCampaign/ActiveCampaign.node.js", "extractedAt": "2025-06-08T10:57:56.032Z"}}, {"success": false, "nodeType": "ActiveCampaignTrigger", "error": "Node source code not found for: ActiveCampaignTrigger"}, {"success": false, "nodeType": "AcuitySchedulingTrigger", "error": "Node source code not found for: AcuitySchedulingTrigger"}, {"success": true, "data": {"nodeType": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "codeLength": 8234, "codeHash": "0fbcb0b60141307fdc3394154af1b2c3133fa6181aac336249c6c211fd24846f", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/Adalo/Adalo.node.js", "extractedAt": "2025-06-08T10:57:57.330Z"}}, {"success": true, "data": {"nodeType": "Affinity", "name": "Affinity", "codeLength": 16217, "codeHash": "e605ea187767403dfa55cd374690f7df563a0baa7ca6991d86d522dc101a2846", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/Affinity/Affinity.node.js", "extractedAt": "2025-06-08T10:57:57.343Z"}}, {"success": false, "nodeType": "AffinityTrigger", "error": "Node source code not found for: AffinityTrigger"}, {"success": true, "data": {"nodeType": "AgileCrm", "name": "AgileCrm", "codeLength": 28115, "codeHash": "ce71c3b5dec23a48d24c5775e9bb79006ce395bed62b306c56340b5c772379c2", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/AgileCrm/AgileCrm.node.js", "extractedAt": "2025-06-08T10:57:57.925Z"}}, {"success": true, "data": {"nodeType": "Airtable", "name": "Airtable", "codeLength": 936, "codeHash": "2d67e72931697178946f5127b43e954649c4c5e7ad9e29764796404ae96e7db5", "hasCredentials": true, "hasPackageInfo": true, "location": "node_modules/n8n-nodes-base/dist/nodes/Airtable/Airtable.node.js", "extractedAt": "2025-06-08T10:57:57.941Z"}}, {"success": false, "nodeType": "AirtableTrigger", "error": "Node source code not found for: AirtableTrigger"}]}}, {"name": "Database Schema Validation", "status": "passed", "startTime": "2025-06-08T10:57:58.574Z", "endTime": "2025-06-08T10:57:58.575Z", "error": null, "details": {"schemaValid": true, "tablesCount": 4, "estimatedStoragePerNode": 16834}}, {"name": "Erro<PERSON>", "status": "passed", "startTime": "2025-06-08T10:57:58.575Z", "endTime": "2025-06-08T10:57:59.244Z", "error": null, "details": {"totalTests": 3, "passed": 2, "results": [{"name": "Non-existent node", "nodeType": "non-existent-package.FakeNode", "expectedError": "not found", "passed": true, "actualError": "Node source code not found for: non-existent-package.FakeNode"}, {"name": "Invalid node type format", "nodeType": "", "expectedError": "invalid", "passed": false, "actualError": "Node source code not found for: "}, {"name": "Malformed package name", "nodeType": "@<EMAIL>", "expectedError": "not found", "passed": true, "actualError": "Node source code not found for: @<EMAIL>"}]}}, {"name": "MCP Server Integration", "status": "passed", "startTime": "2025-06-08T10:57:59.244Z", "endTime": "2025-06-08T10:57:59.249Z", "error": null, "details": {"serverCreated": true, "config": {"port": 3000, "host": "0.0.0.0", "authToken": "test-token"}}}], "extractedNodes": 6, "databaseSchema": {"tables": {"nodes": {"columns": {"id": "UUID PRIMARY KEY", "node_type": "VARCHAR(255) UNIQUE NOT NULL", "name": "VARCHAR(255) NOT NULL", "package_name": "VARCHAR(255)", "display_name": "VARCHAR(255)", "description": "TEXT", "version": "VARCHAR(50)", "code_hash": "VARCHAR(64) NOT NULL", "code_length": "INTEGER NOT NULL", "source_location": "TEXT", "extracted_at": "TIMESTAMP NOT NULL", "updated_at": "TIMESTAMP"}, "indexes": ["node_type", "package_name", "code_hash"]}, "node_source_code": {"columns": {"id": "UUID PRIMARY KEY", "node_id": "UUID REFERENCES nodes(id)", "source_code": "TEXT NOT NULL", "compiled_code": "TEXT", "source_map": "TEXT"}}, "node_credentials": {"columns": {"id": "UUID PRIMARY KEY", "node_id": "UUID REFERENCES nodes(id)", "credential_type": "VARCHAR(255) NOT NULL", "credential_code": "TEXT NOT NULL", "required_fields": "JSONB"}}, "node_metadata": {"columns": {"id": "UUID PRIMARY KEY", "node_id": "UUID REFERENCES nodes(id)", "package_info": "JSONB", "dependencies": "JSONB", "icon": "TEXT", "categories": "TEXT[]", "documentation_url": "TEXT"}}}}}