const fetch = require('node-fetch');

async function testMCPTools() {
  try {
    const response = await fetch('http://localhost:3000/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer secure123'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/list'
      })
    });

    const result = await response.json();
    console.log('MCP Tools Response:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.result && result.result.tools) {
      console.log(`\nTotal tools available: ${result.result.tools.length}`);
      console.log('\nTool names:');
      result.result.tools.forEach(tool => {
        console.log(`- ${tool.name}: ${tool.description.substring(0, 100)}...`);
      });
    }
  } catch (error) {
    console.error('Error testing MCP tools:', error);
  }
}

testMCPTools();
