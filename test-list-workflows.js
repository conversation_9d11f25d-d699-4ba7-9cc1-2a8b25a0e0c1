const fetch = require('node-fetch');

async function testListWorkflows() {
  try {
    const response = await fetch('http://localhost:3000/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer secure123'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'n8n_list_workflows',
          arguments: {}
        }
      })
    });

    const result = await response.json();
    console.log('List Workflows Response:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error listing workflows:', error);
  }
}

testListWorkflows();
