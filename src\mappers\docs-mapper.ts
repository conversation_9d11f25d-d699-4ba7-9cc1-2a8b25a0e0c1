import { promises as fs } from 'fs';
import path from 'path';
import { EnhancedDocumentationFetcher } from '../utils/enhanced-documentation-fetcher';

export class DocsMapper {
  private docsPath = path.join(process.cwd(), 'n8n-docs');
  private enhancedFetcher: EnhancedDocumentationFetcher;

  constructor() {
    this.enhancedFetcher = new EnhancedDocumentationFetcher(this.docsPath);
  }

  // Known documentation mapping fixes
  private readonly KNOWN_FIXES: Record<string, string> = {
    'httpRequest': 'httprequest',
    'code': 'code',
    'webhook': 'webhook',
    'respondToWebhook': 'respondtowebhook',
    // With package prefix
    'n8n-nodes-base.httpRequest': 'httprequest',
    'n8n-nodes-base.code': 'code',
    'n8n-nodes-base.webhook': 'webhook',
    'n8n-nodes-base.respondToWebhook': 'respondtowebhook'
  };

  async fetchDocumentation(nodeType: string): Promise<string | null> {
    try {
      console.log(`📄 Fetching enhanced documentation for: ${nodeType}`);

      // Use the enhanced fetcher to get comprehensive documentation
      const enhancedDoc = await this.enhancedFetcher.getEnhancedNodeDocumentation(nodeType);

      if (enhancedDoc) {
        console.log(`  ✓ Found enhanced docs for: ${nodeType}`);
        return enhancedDoc.markdown;
      }

      // Fallback to the original method if enhanced fetcher fails
      console.log(`  ⚠️  Enhanced fetcher failed, trying fallback for: ${nodeType}`);
      return await this.fallbackFetchDocumentation(nodeType);

    } catch (error) {
      console.error(`  ❌ Error fetching documentation for ${nodeType}:`, error);
      return await this.fallbackFetchDocumentation(nodeType);
    }
  }

  private async fallbackFetchDocumentation(nodeType: string): Promise<string | null> {
    // Apply known fixes first
    const fixedType = this.KNOWN_FIXES[nodeType] || nodeType;

    // Extract node name
    const nodeName = fixedType.split('.').pop()?.toLowerCase();
    if (!nodeName) {
      console.log(`⚠️  Could not extract node name from: ${nodeType}`);
      return null;
    }

    console.log(`📄 Looking for docs for: ${nodeType} -> ${nodeName}`);

    // Try different documentation paths - both files and directories
    const possiblePaths = [
      // Direct file paths
      `docs/integrations/builtin/core-nodes/n8n-nodes-base.${nodeName}.md`,
      `docs/integrations/builtin/app-nodes/n8n-nodes-base.${nodeName}.md`,
      `docs/integrations/builtin/trigger-nodes/n8n-nodes-base.${nodeName}.md`,
      `docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.${nodeName}.md`,
      `docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.${nodeName}.md`,
      // Directory with index.md
      `docs/integrations/builtin/core-nodes/n8n-nodes-base.${nodeName}/index.md`,
      `docs/integrations/builtin/app-nodes/n8n-nodes-base.${nodeName}/index.md`,
      `docs/integrations/builtin/trigger-nodes/n8n-nodes-base.${nodeName}/index.md`,
      `docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.${nodeName}/index.md`,
      `docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.${nodeName}/index.md`
    ];

    // Try each path
    for (const relativePath of possiblePaths) {
      try {
        const fullPath = path.join(this.docsPath, relativePath);
        const content = await fs.readFile(fullPath, 'utf-8');
        console.log(`  ✓ Found docs at: ${relativePath}`);
        return content;
      } catch (error) {
        // File doesn't exist, try next
        continue;
      }
    }

    console.log(`  ✗ No docs found for ${nodeName}`);
    return null;
  }
}