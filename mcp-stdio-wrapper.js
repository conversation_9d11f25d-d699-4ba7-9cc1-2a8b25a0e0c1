#!/usr/bin/env node

// Wrapper to run n8n-mcp in stdio mode for Augment
process.env.MCP_MODE = 'stdio';
process.env.N8N_BASE_URL = 'http://localhost:5678';
process.env.N8N_API_URL = 'http://localhost:5678/rest';
process.env.N8N_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZDc4NTVhMy03ODYwLTRjOTUtYjdjZC0xNjM5NWNmNDI5ZGMiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNzExNzk0fQ.kdz2PdEz56_7bY53P6qtuMZA6T8hvdi9G_lHnVSvJyE';

// Import and run the MCP server
require('./dist/mcp/index.js');
