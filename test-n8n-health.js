const fetch = require('node-fetch');

async function testN8nHealth() {
  try {
    const response = await fetch('http://localhost:3000/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer secure123'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'n8n_health_check',
          arguments: {}
        }
      })
    });

    const result = await response.json();
    console.log('n8n Health Check Response:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error testing n8n health:', error);
  }
}

testN8nHealth();
